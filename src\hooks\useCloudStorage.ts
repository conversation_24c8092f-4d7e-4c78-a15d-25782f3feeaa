import { useState, useCallback, useEffect, useRef } from 'react';
import { 
  StockInfo, 
  UserIdentity, 
  SyncRequest, 
  SyncResponse, 
  SyncStatus,
  UserDataBackup 
} from '@/types/stock';
import { userApi, UserIdentityManager, ApiError } from '@/services/userApi';

/**
 * 云端存储管理 Hook
 */
export function useCloudStorage() {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: navigator.onLine,
    lastSyncTime: null,
    syncInProgress: false,
    hasLocalChanges: false,
    hasCloudData: false,
    conflictDetected: false,
  });

  const [userIdentity, setUserIdentity] = useState<UserIdentity | null>(null);
  const syncTimeoutRef = useRef<number>();

  // 初始化用户标识
  useEffect(() => {
    const initUserIdentity = async () => {
      try {
        const identity = await UserIdentityManager.getCurrentUserIdentity();
        setUserIdentity(identity);
      } catch (error) {
        console.error('Failed to initialize user identity:', error);
      }
    };

    initUserIdentity();
  }, []);

  // 监听网络状态
  useEffect(() => {
    const handleOnline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: true }));
    };

    const handleOffline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: false }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  /**
   * 同步股票数据到云端
   */
  const syncToCloud = useCallback(async (
    localStocks: StockInfo[],
    localLastModified: string,
    forceOverwrite: boolean = false
  ): Promise<SyncResponse['data'] | null> => {
    if (!userIdentity || !syncStatus.isOnline) {
      return null;
    }

    setSyncStatus(prev => ({ ...prev, syncInProgress: true }));

    try {
      const syncRequest: SyncRequest = {
        userIdentity,
        localStocks,
        localLastModified,
        forceOverwrite,
      };

      const result = await userApi.syncStockData(syncRequest);
      
      setSyncStatus(prev => ({
        ...prev,
        syncInProgress: false,
        lastSyncTime: new Date().toISOString(),
        hasLocalChanges: false,
        hasCloudData: true,
        conflictDetected: result.syncAction === 'conflict_resolved',
      }));

      return result;
    } catch (error) {
      console.error('Sync to cloud failed:', error);
      setSyncStatus(prev => ({ ...prev, syncInProgress: false }));
      
      if (error instanceof ApiError && error.status === 0) {
        // 网络错误，标记为离线
        setSyncStatus(prev => ({ ...prev, isOnline: false }));
      }
      
      return null;
    }
  }, [userIdentity, syncStatus.isOnline]);

  /**
   * 从云端获取股票数据
   */
  const getFromCloud = useCallback(async (): Promise<{
    stocks: StockInfo[];
    lastModified: string;
    version: number;
  } | null> => {
    if (!userIdentity || !syncStatus.isOnline) {
      return null;
    }

    try {
      const result = await userApi.getUserStocks(userIdentity.userId!);
      
      setSyncStatus(prev => ({
        ...prev,
        hasCloudData: result.stocks.length > 0,
      }));

      return result;
    } catch (error) {
      console.error('Get from cloud failed:', error);
      
      if (error instanceof ApiError && error.status === 0) {
        setSyncStatus(prev => ({ ...prev, isOnline: false }));
      }
      
      return null;
    }
  }, [userIdentity, syncStatus.isOnline]);

  /**
   * 标记本地数据有变更
   */
  const markLocalChanges = useCallback(() => {
    setSyncStatus(prev => ({ ...prev, hasLocalChanges: true }));
    
    // 延迟自动同步
    if (syncTimeoutRef.current) {
      clearTimeout(syncTimeoutRef.current);
    }
    
    syncTimeoutRef.current = setTimeout(() => {
      // 这里可以触发自动同步逻辑
      console.log('Auto sync triggered by local changes');
    }, 5000); // 5秒后自动同步
  }, []);

  /**
   * 创建数据备份
   */
  const createBackup = useCallback(async (): Promise<UserDataBackup | null> => {
    if (!userIdentity || !syncStatus.isOnline) {
      return null;
    }

    try {
      const backup = await userApi.createBackup(userIdentity.userId!);
      return backup;
    } catch (error) {
      console.error('Create backup failed:', error);
      return null;
    }
  }, [userIdentity, syncStatus.isOnline]);

  /**
   * 从备份恢复数据
   */
  const restoreFromBackup = useCallback(async (backup: UserDataBackup): Promise<boolean> => {
    if (!syncStatus.isOnline) {
      return false;
    }

    try {
      await userApi.restoreFromBackup(backup);
      
      setSyncStatus(prev => ({
        ...prev,
        hasCloudData: true,
        lastSyncTime: new Date().toISOString(),
      }));
      
      return true;
    } catch (error) {
      console.error('Restore from backup failed:', error);
      return false;
    }
  }, [syncStatus.isOnline]);

  /**
   * 删除云端数据
   */
  const deleteCloudData = useCallback(async (): Promise<boolean> => {
    if (!userIdentity || !syncStatus.isOnline) {
      return false;
    }

    try {
      await userApi.deleteUserData(userIdentity.userId!);
      
      setSyncStatus(prev => ({
        ...prev,
        hasCloudData: false,
        lastSyncTime: null,
      }));
      
      return true;
    } catch (error) {
      console.error('Delete cloud data failed:', error);
      return false;
    }
  }, [userIdentity, syncStatus.isOnline]);

  /**
   * 设置自定义用户ID
   */
  const setCustomUserId = useCallback(async (customUserId: string): Promise<boolean> => {
    try {
      UserIdentityManager.setCustomUserId(customUserId);
      const newIdentity = await UserIdentityManager.getCurrentUserIdentity();
      setUserIdentity(newIdentity);
      
      // 重置同步状态
      setSyncStatus(prev => ({
        ...prev,
        hasCloudData: false,
        lastSyncTime: null,
      }));
      
      return true;
    } catch (error) {
      console.error('Set custom user ID failed:', error);
      return false;
    }
  }, []);

  /**
   * 获取用户统计信息
   */
  const getUserStats = useCallback(async () => {
    if (!userIdentity || !syncStatus.isOnline) {
      return null;
    }

    try {
      const stats = await userApi.getUserStats(userIdentity.userId!);
      return stats;
    } catch (error) {
      console.error('Get user stats failed:', error);
      return null;
    }
  }, [userIdentity, syncStatus.isOnline]);

  /**
   * 同步分组数据到云端
   */
  const syncGroupsToCloud = useCallback(async (groups: any[]): Promise<boolean> => {
    if (!userIdentity || !syncStatus.isOnline) {
      return false;
    }

    try {
      await userApi.syncGroupData(userIdentity.userId!, groups);
      return true;
    } catch (error) {
      console.error('分组数据同步失败:', error);
      return false;
    }
  }, [userIdentity, syncStatus.isOnline]);

  /**
   * 从云端获取分组数据
   */
  const getGroupsFromCloud = useCallback(async (): Promise<any[] | null> => {
    if (!userIdentity || !syncStatus.isOnline) {
      return null;
    }

    try {
      const result = await userApi.getUserGroups(userIdentity.userId!);
      return result.groups;
    } catch (error) {
      console.error('获取云端分组数据失败:', error);
      return null;
    }
  }, [userIdentity, syncStatus.isOnline]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }
    };
  }, []);

  return {
    // 状态
    syncStatus,
    userIdentity,

    // 方法
    syncToCloud,
    getFromCloud,
    syncGroupsToCloud,
    getGroupsFromCloud,
    markLocalChanges,
    createBackup,
    restoreFromBackup,
    deleteCloudData,
    setCustomUserId,
    getUserStats,
  };
}
