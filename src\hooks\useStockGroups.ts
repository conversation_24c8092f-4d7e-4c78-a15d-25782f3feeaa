import { useState, useCallback, useEffect, useMemo } from 'react';
import {
  StockGroup,
  CreateGroupRequest,
  UpdateGroupRequest,
  GroupOperationResult,
  MoveStockToGroupRequest,
  GroupStats,
  UseStockGroupsResult
} from '@/types/stock';
import { useCloudStorage } from './useCloudStorage';
import { validateGroupData } from '@/utils/dataMigration';

// 默认分组配置
const DEFAULT_GROUP: Omit<StockGroup, 'id'> = {
  name: '默认分组',
  order: 0,
  createdAt: '',
  isDefault: true,
  color: '#6B7280',
  description: '未分组的股票将自动归入此分组'
};

// 本地存储键
const STORAGE_KEY = 'gupiao-stock-groups';

/**
 * 生成分组ID
 */
function generateGroupId(): string {
  return `group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 创建默认分组
 */
function createDefaultGroup(): StockGroup {
  return {
    ...DEFAULT_GROUP,
    id: 'default',
    createdAt: new Date().toISOString(),
  };
}

/**
 * 股票分组管理Hook
 */
export function useStockGroups(): UseStockGroupsResult {
  const [groups, setGroups] = useState<StockGroup[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [, setLastModified] = useState<string>(new Date().toISOString());

  // 云端存储管理
  const cloudStorage = useCloudStorage();

  // 获取默认分组
  const defaultGroup = useMemo(() => {
    return groups.find(group => group.isDefault) || null;
  }, [groups]);

  // 初始化数据加载
  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);
      try {
        // 首先从 localStorage 加载数据
        const savedGroups = localStorage.getItem(STORAGE_KEY);
        const savedLastModified = localStorage.getItem(`${STORAGE_KEY}_lastModified`);

        let localGroups: StockGroup[] = [];
        let localLastModified = new Date().toISOString();

        if (savedGroups) {
          try {
            localGroups = JSON.parse(savedGroups);
            localLastModified = savedLastModified || localLastModified;

            // 验证分组数据完整性
            const validation = validateGroupData(localGroups);
            if (!validation.isValid) {
              console.warn('分组数据验证失败，重新创建默认分组:', validation.errors);
              localGroups = [];
            }
          } catch (error) {
            console.error('解析分组数据失败:', error);
            localGroups = [];
          }
        }

        // 确保存在默认分组
        if (!localGroups.some(group => group.isDefault)) {
          const defaultGroup = createDefaultGroup();
          localGroups = [defaultGroup, ...localGroups];
          // 保存修复后的数据
          saveToStorage(localGroups);
        }

        // 设置本地数据
        setGroups(localGroups);
        setLastModified(localLastModified);

        // 如果在线，尝试从云端同步数据
        if (cloudStorage.syncStatus.isOnline && cloudStorage.userIdentity) {
          try {
            const cloudGroups = await cloudStorage.getGroupsFromCloud();

            if (cloudGroups && cloudGroups.length > 0) {
              // 确保云端数据也有默认分组
              let finalCloudGroups = cloudGroups;
              if (!cloudGroups.some(group => group.isDefault)) {
                const defaultGroup = createDefaultGroup();
                finalCloudGroups = [defaultGroup, ...cloudGroups];
              }

              // 比较时间戳决定使用哪个数据
              // 这里简化处理，优先使用云端数据
              setGroups(finalCloudGroups);
              saveToStorage(finalCloudGroups);
            } else if (localGroups.length > 0) {
              // 云端无数据，上传本地数据
              await syncGroupsToCloud(localGroups);
            }
          } catch (cloudError) {
            console.warn('云端分组数据同步失败，使用本地数据:', cloudError);
          }
        }
      } catch (err) {
        console.error('初始化分组数据失败:', err);
        setError('初始化分组数据失败');
      } finally {
        setIsLoading(false);
      }
    };

    if (cloudStorage.userIdentity) {
      initializeData();
    }
  }, [cloudStorage.userIdentity, cloudStorage.syncStatus.isOnline]);

  // 保存分组列表到localStorage和云端
  const saveToStorage = useCallback((groupList: StockGroup[], timestamp?: string) => {
    const saveTime = timestamp || new Date().toISOString();

    try {
      // 保存到 localStorage
      localStorage.setItem(STORAGE_KEY, JSON.stringify(groupList));
      localStorage.setItem(`${STORAGE_KEY}_lastModified`, saveTime);

      setLastModified(saveTime);

      // 标记本地数据有变更，触发云端同步
      if (cloudStorage.syncStatus.isOnline) {
        cloudStorage.markLocalChanges();

        // 异步同步到云端
        syncGroupsToCloud(groupList).catch(error => {
          console.warn('云端分组同步失败:', error);
        });
      }
    } catch (err) {
      console.error('保存分组列表失败:', err);
      setError('保存分组列表失败');
    }
  }, [cloudStorage]);

  // 同步分组到云端
  const syncGroupsToCloud = useCallback(async (groupList: StockGroup[]) => {
    if (!cloudStorage.syncStatus.isOnline || !cloudStorage.userIdentity) {
      return;
    }

    try {
      // 使用新的分组同步API
      const success = await cloudStorage.syncGroupsToCloud(groupList);
      if (success) {
        console.log('分组数据同步到云端成功');
      } else {
        console.warn('分组数据同步到云端失败');
      }
    } catch (error) {
      console.error('分组云端同步失败:', error);
    }
  }, [cloudStorage]);

  // 创建分组
  const createGroup = useCallback(async (request: CreateGroupRequest): Promise<GroupOperationResult> => {
    setIsLoading(true);
    setError(null);

    try {
      // 验证分组名称
      if (!request.name.trim()) {
        return { success: false, message: '分组名称不能为空' };
      }

      // 检查重复名称
      if (groups.some(group => group.name === request.name.trim())) {
        return { success: false, message: '分组名称已存在' };
      }

      // 创建新分组
      const newGroup: StockGroup = {
        id: generateGroupId(),
        name: request.name.trim(),
        order: groups.length, // 新分组排在最后
        createdAt: new Date().toISOString(),
        isDefault: false,
        color: request.color || '#3B82F6',
        description: request.description || '',
      };

      const updatedGroups = [...groups, newGroup];
      setGroups(updatedGroups);
      saveToStorage(updatedGroups);

      return { success: true, message: '分组创建成功', group: newGroup };
    } catch (err) {
      console.error('创建分组失败:', err);
      const errorMessage = '创建分组失败';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [groups, saveToStorage]);

  // 更新分组
  const updateGroup = useCallback(async (request: UpdateGroupRequest): Promise<GroupOperationResult> => {
    setIsLoading(true);
    setError(null);

    try {
      const groupIndex = groups.findIndex(group => group.id === request.id);
      if (groupIndex === -1) {
        return { success: false, message: '分组不存在' };
      }

      const existingGroup = groups[groupIndex];

      // 不允许修改默认分组的某些属性
      if (existingGroup.isDefault && request.name && request.name !== existingGroup.name) {
        return { success: false, message: '不能修改默认分组的名称' };
      }

      // 检查名称重复（排除自己）
      if (request.name && request.name.trim() !== existingGroup.name) {
        if (groups.some(group => group.id !== request.id && group.name === request.name!.trim())) {
          return { success: false, message: '分组名称已存在' };
        }
      }

      // 更新分组
      const updatedGroup: StockGroup = {
        ...existingGroup,
        ...(request.name && { name: request.name.trim() }),
        ...(request.color && { color: request.color }),
        ...(request.description !== undefined && { description: request.description }),
        ...(request.order !== undefined && { order: request.order }),
      };

      const updatedGroups = [...groups];
      updatedGroups[groupIndex] = updatedGroup;

      setGroups(updatedGroups);
      saveToStorage(updatedGroups);

      return { success: true, message: '分组更新成功', group: updatedGroup };
    } catch (err) {
      console.error('更新分组失败:', err);
      const errorMessage = '更新分组失败';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [groups, saveToStorage]);

  // 删除分组
  const deleteGroup = useCallback(async (groupId: string): Promise<GroupOperationResult> => {
    setIsLoading(true);
    setError(null);

    try {
      const groupToDelete = groups.find(group => group.id === groupId);
      if (!groupToDelete) {
        return { success: false, message: '分组不存在' };
      }

      // 不允许删除默认分组
      if (groupToDelete.isDefault) {
        return { success: false, message: '不能删除默认分组' };
      }

      const updatedGroups = groups.filter(group => group.id !== groupId);
      setGroups(updatedGroups);
      saveToStorage(updatedGroups);

      return { success: true, message: '分组删除成功' };
    } catch (err) {
      console.error('删除分组失败:', err);
      const errorMessage = '删除分组失败';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [groups, saveToStorage]);

  // 重新排序分组
  const reorderGroups = useCallback(async (groupIds: string[]): Promise<boolean> => {
    try {
      const reorderedGroups = groupIds.map((id, index) => {
        const group = groups.find(g => g.id === id);
        if (!group) return null;
        return { ...group, order: index };
      }).filter(Boolean) as StockGroup[];

      // 确保所有分组都被包含
      const missingGroups = groups.filter(group => !groupIds.includes(group.id));
      const finalGroups = [...reorderedGroups, ...missingGroups];

      setGroups(finalGroups);
      saveToStorage(finalGroups);

      return true;
    } catch (err) {
      console.error('重新排序分组失败:', err);
      setError('重新排序分组失败');
      return false;
    }
  }, [groups, saveToStorage]);

  // 获取分组统计信息
  const getGroupStats = useCallback((groupId: string): GroupStats | null => {
    const group = groups.find(g => g.id === groupId);
    if (!group) return null;

    // 这里需要与股票数据集成，暂时返回基础信息
    return {
      id: group.id,
      name: group.name,
      stockCount: 0, // 需要从股票列表中计算
      lastUpdate: group.createdAt,
    };
  }, [groups]);

  // 移动股票到分组（这个方法会在useStockList集成时实现）
  const moveStockToGroup = useCallback(async (request: MoveStockToGroupRequest): Promise<boolean> => {
    try {
      // 验证目标分组存在（如果不是移动到默认分组）
      if (request.targetGroupId && !groups.some(group => group.id === request.targetGroupId)) {
        setError('目标分组不存在');
        return false;
      }

      // 这个功能需要与useStockList集成，暂时返回成功
      console.log('移动股票到分组:', request);
      return true;
    } catch (err) {
      console.error('移动股票到分组失败:', err);
      setError('移动股票到分组失败');
      return false;
    }
  }, [groups]);

  return {
    groups: groups.sort((a, b) => a.order - b.order), // 按order排序返回
    defaultGroup,
    isLoading,
    error,
    createGroup,
    updateGroup,
    deleteGroup,
    reorderGroups,
    getGroupStats,
    moveStockToGroup,
  };
}
