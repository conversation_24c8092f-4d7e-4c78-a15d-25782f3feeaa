import { useState, useCallback } from 'react';
import {
  Plus,
  Edit2,
  Trash2,
  Check,
  X,
  <PERSON>ert<PERSON><PERSON><PERSON>,
  Folder,
  GripVertical,
  Settings
} from 'lucide-react';
import { useStockGroups } from '@/hooks/useStockGroups';
import { CreateGroupRequest, UpdateGroupRequest } from '@/types/stock';

interface GroupManagerProps {
  isFullScreen?: boolean;
  onGroupSelect?: (groupId: string) => void;
  selectedGroupId?: string;
}

interface GroupFormData {
  name: string;
  color: string;
  description: string;
}

export function GroupManager({ 
  isFullScreen = false, 
  onGroupSelect,
  selectedGroupId 
}: GroupManagerProps) {
  const {
    groups,
    isLoading,
    error,
    createGroup,
    updateGroup,
    deleteGroup,
    getGroupStats
  } = useStockGroups();

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingGroupId, setEditingGroupId] = useState<string | null>(null);
  const [deletingGroupId, setDeletingGroupId] = useState<string | null>(null);
  const [formData, setFormData] = useState<GroupFormData>({
    name: '',
    color: '#3B82F6',
    description: ''
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  // 显示通知
  const showNotification = useCallback((type: 'success' | 'error', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 3000);
  }, []);

  // 验证表单数据
  const validateForm = useCallback((data: GroupFormData): Record<string, string> => {
    const errors: Record<string, string> = {};

    if (!data.name.trim()) {
      errors.name = '分组名称不能为空';
    } else if (data.name.trim().length > 20) {
      errors.name = '分组名称不能超过20个字符';
    } else if (groups.some(group => 
      group.name === data.name.trim() && group.id !== editingGroupId
    )) {
      errors.name = '分组名称已存在';
    }

    if (!data.color || !/^#[0-9A-Fa-f]{6}$/.test(data.color)) {
      errors.color = '请选择有效的颜色';
    }

    if (data.description.length > 100) {
      errors.description = '描述不能超过100个字符';
    }

    return errors;
  }, [groups, editingGroupId]);

  // 重置表单
  const resetForm = useCallback(() => {
    setFormData({
      name: '',
      color: '#3B82F6',
      description: ''
    });
    setFormErrors({});
    setShowCreateForm(false);
    setEditingGroupId(null);
  }, []);

  // 处理创建分组
  const handleCreateGroup = useCallback(async () => {
    const errors = validateForm(formData);
    setFormErrors(errors);

    if (Object.keys(errors).length > 0) {
      return;
    }

    const request: CreateGroupRequest = {
      name: formData.name.trim(),
      color: formData.color,
      description: formData.description.trim()
    };

    const result = await createGroup(request);
    if (result.success) {
      showNotification('success', result.message || '分组创建成功');
      resetForm();
    } else {
      showNotification('error', result.message || '创建分组失败');
    }
  }, [formData, validateForm, createGroup, showNotification, resetForm]);

  // 处理编辑分组
  const handleEditGroup = useCallback(async () => {
    if (!editingGroupId) return;

    const errors = validateForm(formData);
    setFormErrors(errors);

    if (Object.keys(errors).length > 0) {
      return;
    }

    const request: UpdateGroupRequest = {
      id: editingGroupId,
      name: formData.name.trim(),
      color: formData.color,
      description: formData.description.trim()
    };

    const result = await updateGroup(request);
    if (result.success) {
      showNotification('success', result.message || '分组更新成功');
      resetForm();
    } else {
      showNotification('error', result.message || '更新分组失败');
    }
  }, [editingGroupId, formData, validateForm, updateGroup, showNotification, resetForm]);

  // 开始编辑分组
  const startEditGroup = useCallback((groupId: string) => {
    const group = groups.find(g => g.id === groupId);
    if (!group) return;

    setFormData({
      name: group.name,
      color: group.color || '#3B82F6',
      description: group.description || ''
    });
    setEditingGroupId(groupId);
    setShowCreateForm(true);
  }, [groups]);

  // 处理删除分组
  const handleDeleteGroup = useCallback(async (groupId: string) => {
    const result = await deleteGroup(groupId);
    if (result.success) {
      showNotification('success', result.message || '分组删除成功');
      setDeletingGroupId(null);
    } else {
      showNotification('error', result.message || '删除分组失败');
    }
  }, [deleteGroup, showNotification]);

  // 处理表单输入变化
  const handleInputChange = useCallback((field: keyof GroupFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  }, [formErrors]);

  // 预定义颜色选项
  const colorOptions = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', 
    '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'
  ];

  return (
    <div className={`bg-white rounded-lg shadow-sm border ${isFullScreen ? 'h-full' : ''}`}>
      {/* 头部 */}
      <div className={`flex items-center justify-between p-4 border-b ${isFullScreen ? 'px-6' : ''}`}>
        <div className="flex items-center gap-2">
          <Settings className={`${isFullScreen ? 'w-6 h-6' : 'w-5 h-5'} text-gray-600`} />
          <h3 className={`${isFullScreen ? 'text-lg' : 'text-base'} font-medium text-gray-900`}>
            分组管理
          </h3>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          disabled={isLoading}
          className={`btn btn-primary flex items-center gap-2 ${isFullScreen ? 'px-4 py-2 text-base' : 'px-3 py-1.5 text-sm'}`}
        >
          <Plus className={`${isFullScreen ? 'w-5 h-5' : 'w-4 h-4'}`} />
          新建分组
        </button>
      </div>

      {/* 通知 */}
      {notification && (
        <div className={`mx-4 mt-4 p-3 rounded-md ${
          notification.type === 'success' 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          <div className="flex items-center gap-2">
            {notification.type === 'success' ? (
              <Check className="w-4 h-4" />
            ) : (
              <AlertCircle className="w-4 h-4" />
            )}
            <span className="text-sm">{notification.message}</span>
          </div>
        </div>
      )}

      {/* 错误显示 */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-red-50 text-red-800 border border-red-200 rounded-md">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4" />
            <span className="text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* 创建/编辑表单 */}
      {showCreateForm && (
        <div className={`p-4 border-b bg-gray-50 ${isFullScreen ? 'px-6' : ''}`}>
          <h4 className={`${isFullScreen ? 'text-base' : 'text-sm'} font-medium text-gray-900 mb-3`}>
            {editingGroupId ? '编辑分组' : '创建新分组'}
          </h4>
          
          <div className="space-y-3">
            {/* 分组名称 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                分组名称 *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="请输入分组名称"
                className={`input ${formErrors.name ? 'border-red-500 focus:ring-red-500' : ''}`}
                maxLength={20}
              />
              {formErrors.name && (
                <p className="mt-1 text-sm text-red-600">{formErrors.name}</p>
              )}
            </div>

            {/* 分组颜色 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                分组颜色
              </label>
              <div className="flex items-center gap-2">
                <div className="flex gap-1">
                  {colorOptions.map(color => (
                    <button
                      key={color}
                      onClick={() => handleInputChange('color', color)}
                      className={`w-6 h-6 rounded border-2 ${
                        formData.color === color ? 'border-gray-400' : 'border-gray-200'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
                <input
                  type="color"
                  value={formData.color}
                  onChange={(e) => handleInputChange('color', e.target.value)}
                  className="w-8 h-6 border border-gray-300 rounded cursor-pointer"
                />
              </div>
              {formErrors.color && (
                <p className="mt-1 text-sm text-red-600">{formErrors.color}</p>
              )}
            </div>

            {/* 分组描述 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                描述（可选）
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="请输入分组描述"
                rows={2}
                className={`input resize-none ${formErrors.description ? 'border-red-500 focus:ring-red-500' : ''}`}
                maxLength={100}
              />
              <div className="flex justify-between items-center mt-1">
                {formErrors.description && (
                  <p className="text-sm text-red-600">{formErrors.description}</p>
                )}
                <p className="text-xs text-gray-500 ml-auto">
                  {formData.description.length}/100
                </p>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-2 pt-2">
              <button
                onClick={editingGroupId ? handleEditGroup : handleCreateGroup}
                disabled={isLoading}
                className="btn btn-primary flex items-center gap-2"
              >
                <Check className="w-4 h-4" />
                {editingGroupId ? '保存' : '创建'}
              </button>
              <button
                onClick={resetForm}
                className="btn btn-secondary flex items-center gap-2"
              >
                <X className="w-4 h-4" />
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 分组列表 */}
      <div className={`${isFullScreen ? 'px-6 py-4' : 'p-4'}`}>
        {isLoading && groups.length === 0 ? (
          <div className="text-center py-8">
            <div className="inline-block w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <p className="mt-2 text-sm text-gray-500">加载中...</p>
          </div>
        ) : groups.length === 0 ? (
          <div className="text-center py-8">
            <Folder className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">暂无分组</p>
            <p className="text-xs text-gray-400 mt-1">点击"新建分组"创建第一个分组</p>
          </div>
        ) : (
          <div className="space-y-2">
            {groups.map((group) => {
              const stats = getGroupStats(group.id);
              const isSelected = selectedGroupId === group.id;
              
              return (
                <div
                  key={group.id}
                  className={`group relative p-3 border rounded-lg transition-colors ${
                    isSelected 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div 
                      className="flex items-center gap-3 flex-1 cursor-pointer"
                      onClick={() => onGroupSelect?.(group.id)}
                    >
                      {/* 拖拽手柄 */}
                      <GripVertical className="w-4 h-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                      
                      {/* 分组颜色指示器 */}
                      <div 
                        className="w-3 h-3 rounded-full border border-gray-300"
                        style={{ backgroundColor: group.color }}
                      />
                      
                      {/* 分组信息 */}
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium text-gray-900">
                            {group.name}
                            {group.isDefault && (
                              <span className="ml-2 text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded">
                                默认
                              </span>
                            )}
                          </h4>
                          {stats && (
                            <span className="text-sm text-gray-500">
                              ({stats.stockCount} 只股票)
                            </span>
                          )}
                        </div>
                        {group.description && (
                          <p className="text-sm text-gray-500 mt-1">{group.description}</p>
                        )}
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        onClick={() => startEditGroup(group.id)}
                        className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded"
                        title="编辑分组"
                      >
                        <Edit2 className="w-4 h-4" />
                      </button>
                      {!group.isDefault && (
                        <button
                          onClick={() => setDeletingGroupId(group.id)}
                          className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded"
                          title="删除分组"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* 删除确认对话框 */}
      {deletingGroupId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm mx-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                <AlertCircle className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">确认删除</h3>
                <p className="text-sm text-gray-500">此操作不可撤销</p>
              </div>
            </div>
            <p className="text-sm text-gray-700 mb-4">
              确定要删除这个分组吗？分组内的股票将移动到默认分组。
            </p>
            <div className="flex gap-2">
              <button
                onClick={() => handleDeleteGroup(deletingGroupId)}
                disabled={isLoading}
                className="btn btn-danger flex-1"
              >
                删除
              </button>
              <button
                onClick={() => setDeletingGroupId(null)}
                className="btn btn-secondary flex-1"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
