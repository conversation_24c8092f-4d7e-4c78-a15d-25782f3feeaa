import { EastmoneyApiResponse, ApiResponse } from '../types/api';
import { generateEastmoneyHeaders } from '../utils/headers';
import { fetchWithRetry, RateLimiter } from '../utils/retry';
import { 
  validateStockCode, 
  validateEastmoneyResponse, 
  validateKlineData,
  sanitizeStockCode,
  safeJsonParse 
} from './validator';

/**
 * 东方财富API服务类
 */
export class EastmoneyApiService {
  private readonly baseUrl = 'https://push2.eastmoney.com';
  private readonly rateLimiter: RateLimiter;
  
  constructor() {
    // 限制每分钟最多60个请求
    this.rateLimiter = new RateLimiter(60, 60000);
  }

  /**
   * 获取股票资金流向数据
   * @param stockCode 股票代码
   * @param limit 数据条数限制
   * @returns 资金流向数据
   */
  async getStockFlowData(stockCode: string, limit: number = 240): Promise<ApiResponse<any>> {
    try {
      // 验证和清洗股票代码
      const cleanCode = sanitizeStockCode(stockCode);
      if (!validateStockCode(cleanCode)) {
        return {
          success: false,
          message: '无效的股票代码',
          timestamp: new Date().toISOString(),
        };
      }

      // 等待频率限制
      await this.rateLimiter.waitForSlot();

      // 构建请求URL
      const url = this.buildFlowDataUrl(cleanCode, limit);
      
      // 生成请求头
      const headers = generateEastmoneyHeaders(cleanCode);

      // 发送请求
      const response = await fetchWithRetry(url, {
        method: 'GET',
        headers,
      }, {
        maxAttempts: 3,
        baseDelay: 1000,
        retryCondition: (error) => {
          if (error instanceof Response) {
            return error.status >= 500 || error.status === 429;
          }
          return true;
        }
      });

      // 解析响应
      const responseText = await response.text();
      const parseResult = safeJsonParse<EastmoneyApiResponse>(responseText);
      
      if (!parseResult.success) {
        return {
          success: false,
          message: `数据解析失败: ${parseResult.error}`,
          timestamp: new Date().toISOString(),
        };
      }

      const apiData = parseResult.data!;

      // 验证响应数据
      if (!validateEastmoneyResponse(apiData)) {
        const responseData = apiData as any; // 类型断言以避免TypeScript推断错误
        console.error('Eastmoney response validation failed', {
          stockCode: cleanCode,
          responseStructure: {
            rc: responseData?.rc,
            rt: responseData?.rt,
            hasData: !!responseData?.data,
            dataKeys: responseData?.data ? Object.keys(responseData.data) : [],
            dataCode: responseData?.data?.code,
            dataName: responseData?.data?.name,
            dataMarket: responseData?.data?.market,
            dataMarketType: typeof responseData?.data?.market,
            hasKlines: Array.isArray(responseData?.data?.klines),
            klinesLength: responseData?.data?.klines?.length,
          },
          fullResponse: JSON.stringify(responseData).substring(0, 500)
        });

        return {
          success: false,
          message: '响应数据格式无效',
          timestamp: new Date().toISOString(),
        };
      }

      // 检查API返回码
      if (apiData.rc !== 0) {
        return {
          success: false,
          message: `API返回错误码: ${apiData.rc}`,
          timestamp: new Date().toISOString(),
        };
      }

      // 处理和验证数据
      const processedData = this.processFlowData(apiData);
      
      return {
        success: true,
        data: processedData,
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      console.error('获取股票资金流向数据失败:', error);
      
      return {
        success: false,
        message: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * 批量获取多个股票的资金流向数据
   * @param stockCodes 股票代码数组
   * @param limit 每个股票的数据条数
   * @returns 批量数据结果
   */
  async getBatchFlowData(stockCodes: string[], limit: number = 240): Promise<ApiResponse<Record<string, any>>> {
    try {
      const results: Record<string, any> = {};
      const errors: string[] = [];

      // 并发获取数据，但控制并发数量
      const batchSize = 5;
      for (let i = 0; i < stockCodes.length; i += batchSize) {
        const batch = stockCodes.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async (code) => {
          const result = await this.getStockFlowData(code, limit);
          return { code, result };
        });

        const batchResults = await Promise.all(batchPromises);
        
        for (const { code, result } of batchResults) {
          if (result.success) {
            results[code] = result.data;
          } else {
            errors.push(`${code}: ${result.message}`);
          }
        }

        // 批次间添加延迟
        if (i + batchSize < stockCodes.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      return {
        success: Object.keys(results).length > 0,
        data: results,
        message: errors.length > 0 ? `部分请求失败: ${errors.join('; ')}` : undefined,
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '批量获取失败',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * 构建资金流向数据请求URL
   * @param stockCode 股票代码
   * @param limit 数据条数
   * @returns 完整URL
   */
  private buildFlowDataUrl(stockCode: string, limit: number): string {
    // 确定市场代码：沪市=1，深市=0
    const marketCode = stockCode.startsWith('6') ? 1 : 0;
    const secid = `${marketCode}.${stockCode}`;
    
    const params = new URLSearchParams({
      secid,
      klt: '1', // 1分钟K线
      lmt: limit.toString(),
      fields1: 'f1,f2,f3,f7',
      fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63',
    });

    return `${this.baseUrl}/api/qt/stock/fflow/kline/get?${params.toString()}`;
  }

  /**
   * 处理资金流向数据
   * @param apiData 原始API数据
   * @returns 处理后的数据
   */
  private processFlowData(apiData: EastmoneyApiResponse): any {
    const { data } = apiData;
    const processedKlines: any[] = [];

    // 处理K线数据
    for (const kline of data.klines) {
      if (!validateKlineData(kline)) {
        continue; // 跳过无效数据
      }

      const parts = kline.split(',');
      const [time, mainNet, superLargeNet, largeNet, mediumNet, smallNet] = parts;

      processedKlines.push({
        time,
        mainNetInflow: parseFloat(mainNet),
        superLargeNetInflow: parseFloat(superLargeNet),
        largeNetInflow: parseFloat(largeNet),
        mediumNetInflow: parseFloat(mediumNet),
        smallNetInflow: parseFloat(smallNet),
      });
    }

    // 计算汇总数据
    const latest = processedKlines[processedKlines.length - 1];
    const summary = latest ? {
      code: data.code,
      name: data.name,
      market: data.market,
      lastUpdate: latest.time,
      mainNetInflow: latest.mainNetInflow,
      superLargeNetInflow: latest.superLargeNetInflow,
      largeNetInflow: latest.largeNetInflow,
      mediumNetInflow: latest.mediumNetInflow,
      smallNetInflow: latest.smallNetInflow,
    } : null;

    return {
      summary,
      klines: processedKlines,
      totalCount: processedKlines.length,
      tradePeriods: data.tradePeriods,
    };
  }

  /**
   * 获取服务状态
   * @returns 服务状态信息
   */
  getServiceStatus(): {
    isHealthy: boolean;
    rateLimitStatus: {
      canMakeRequest: boolean;
      nextAvailableTime: number;
    };
  } {
    return {
      isHealthy: true,
      rateLimitStatus: {
        canMakeRequest: this.rateLimiter.canMakeRequest(),
        nextAvailableTime: this.rateLimiter.getNextAvailableTime(),
      },
    };
  }
}
