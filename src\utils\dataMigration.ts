import { StockGroup, StockInfo, UserStockData } from '@/types/stock';

// 数据版本常量
export const DATA_VERSION = {
  V1: 1, // 原始版本，无分组功能
  V2: 2, // 当前版本，支持分组功能
  CURRENT: 2
} as const;

// 迁移结果类型
export interface MigrationResult {
  success: boolean;
  version: number;
  migratedStocks: StockInfo[];
  migratedGroups: StockGroup[];
  message: string;
  warnings: string[];
}

// 迁移状态类型
export interface MigrationStatus {
  isRequired: boolean;
  fromVersion: number;
  toVersion: number;
  estimatedTime: number; // 预估迁移时间（毫秒）
}

/**
 * 创建默认分组
 */
export function createDefaultGroup(): StockGroup {
  return {
    id: 'default',
    name: '默认分组',
    order: 0,
    createdAt: new Date().toISOString(),
    isDefault: true,
    color: '#6B7280',
    description: '未分组的股票将自动归入此分组'
  };
}

/**
 * 检测数据版本和迁移需求
 */
export function detectMigrationStatus(data: any): MigrationStatus {
  // 检测数据版本
  let currentVersion = 1;
  
  if (data && typeof data === 'object') {
    // 检查是否有版本字段
    if (data.version && typeof data.version === 'number') {
      currentVersion = data.version;
    }
    // 检查是否有分组字段（V2特征）
    else if (data.groups && Array.isArray(data.groups)) {
      currentVersion = 2;
    }
    // 检查股票数据中是否有groupId字段
    else if (data.stocks && Array.isArray(data.stocks) && 
             data.stocks.some((stock: any) => stock.groupId !== undefined)) {
      currentVersion = 2;
    }
  }

  const isRequired = currentVersion < DATA_VERSION.CURRENT;
  const estimatedTime = isRequired ? Math.max(data?.stocks?.length * 10 || 100, 500) : 0;

  return {
    isRequired,
    fromVersion: currentVersion,
    toVersion: DATA_VERSION.CURRENT,
    estimatedTime
  };
}

/**
 * 验证股票数据格式
 */
export function validateStockData(stocks: any[]): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!Array.isArray(stocks)) {
    errors.push('股票数据必须是数组格式');
    return { isValid: false, errors };
  }

  stocks.forEach((stock, index) => {
    if (!stock || typeof stock !== 'object') {
      errors.push(`股票 ${index + 1}: 数据格式无效`);
      return;
    }

    if (!stock.code || typeof stock.code !== 'string') {
      errors.push(`股票 ${index + 1}: 缺少有效的股票代码`);
    }

    if (!stock.name || typeof stock.name !== 'string') {
      errors.push(`股票 ${index + 1}: 缺少有效的股票名称`);
    }

    // 检查addedAt字段
    if (stock.addedAt && typeof stock.addedAt !== 'string') {
      errors.push(`股票 ${index + 1}: addedAt字段格式无效`);
    }

    // 检查groupId字段（如果存在）
    if (stock.groupId !== undefined && typeof stock.groupId !== 'string') {
      errors.push(`股票 ${index + 1}: groupId字段格式无效`);
    }
  });

  return { isValid: errors.length === 0, errors };
}

/**
 * 验证分组数据格式
 */
export function validateGroupData(groups: any[]): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!Array.isArray(groups)) {
    errors.push('分组数据必须是数组格式');
    return { isValid: false, errors };
  }

  const groupIds = new Set<string>();
  const groupNames = new Set<string>();
  let hasDefaultGroup = false;

  groups.forEach((group, index) => {
    if (!group || typeof group !== 'object') {
      errors.push(`分组 ${index + 1}: 数据格式无效`);
      return;
    }

    // 检查必需字段
    if (!group.id || typeof group.id !== 'string') {
      errors.push(`分组 ${index + 1}: 缺少有效的分组ID`);
    } else if (groupIds.has(group.id)) {
      errors.push(`分组 ${index + 1}: 分组ID重复 (${group.id})`);
    } else {
      groupIds.add(group.id);
    }

    if (!group.name || typeof group.name !== 'string') {
      errors.push(`分组 ${index + 1}: 缺少有效的分组名称`);
    } else if (groupNames.has(group.name)) {
      errors.push(`分组 ${index + 1}: 分组名称重复 (${group.name})`);
    } else {
      groupNames.add(group.name);
    }

    if (typeof group.order !== 'number') {
      errors.push(`分组 ${index + 1}: order字段必须是数字`);
    }

    if (!group.createdAt || typeof group.createdAt !== 'string') {
      errors.push(`分组 ${index + 1}: 缺少有效的创建时间`);
    }

    // 检查是否有默认分组
    if (group.isDefault === true) {
      if (hasDefaultGroup) {
        errors.push(`分组 ${index + 1}: 不能有多个默认分组`);
      }
      hasDefaultGroup = true;
    }
  });

  if (!hasDefaultGroup && groups.length > 0) {
    errors.push('必须至少有一个默认分组');
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * 执行数据迁移
 */
export function migrateData(data: any): MigrationResult {
  const warnings: string[] = [];
  const migrationStatus = detectMigrationStatus(data);

  // 如果不需要迁移，直接返回
  if (!migrationStatus.isRequired) {
    return {
      success: true,
      version: DATA_VERSION.CURRENT,
      migratedStocks: data.stocks || [],
      migratedGroups: data.groups || [],
      message: '数据已是最新版本，无需迁移',
      warnings: []
    };
  }

  try {
    // 初始化迁移数据
    let migratedStocks: StockInfo[] = [];
    let migratedGroups: StockGroup[] = [];

    // 处理股票数据
    if (data.stocks && Array.isArray(data.stocks)) {
      // 验证股票数据
      const stockValidation = validateStockData(data.stocks);
      if (!stockValidation.isValid) {
        return {
          success: false,
          version: migrationStatus.fromVersion,
          migratedStocks: [],
          migratedGroups: [],
          message: '股票数据验证失败: ' + stockValidation.errors.join(', '),
          warnings: []
        };
      }

      // 迁移股票数据
      migratedStocks = data.stocks.map((stock: any) => {
        const migratedStock: StockInfo = {
          code: stock.code,
          name: stock.name,
          addedAt: stock.addedAt || new Date().toISOString(),
          groupId: stock.groupId || 'default' // 无分组的股票归入默认分组
        };

        if (!stock.groupId) {
          warnings.push(`股票 ${stock.code} 已自动归入默认分组`);
        }

        return migratedStock;
      });
    }

    // 处理分组数据
    if (data.groups && Array.isArray(data.groups)) {
      // 验证分组数据
      const groupValidation = validateGroupData(data.groups);
      if (!groupValidation.isValid) {
        warnings.push('分组数据验证失败，将重新创建默认分组: ' + groupValidation.errors.join(', '));
        migratedGroups = [createDefaultGroup()];
      } else {
        migratedGroups = [...data.groups];
      }
    } else {
      // 创建默认分组
      migratedGroups = [createDefaultGroup()];
      warnings.push('未找到分组数据，已创建默认分组');
    }

    // 确保有默认分组
    if (!migratedGroups.some(group => group.isDefault)) {
      migratedGroups.unshift(createDefaultGroup());
      warnings.push('已添加缺失的默认分组');
    }

    // 验证股票的分组引用
    const groupIds = new Set(migratedGroups.map(group => group.id));
    migratedStocks = migratedStocks.map(stock => {
      if (stock.groupId && !groupIds.has(stock.groupId)) {
        warnings.push(`股票 ${stock.code} 的分组 ${stock.groupId} 不存在，已移动到默认分组`);
        return { ...stock, groupId: 'default' };
      }
      return stock;
    });

    return {
      success: true,
      version: DATA_VERSION.CURRENT,
      migratedStocks,
      migratedGroups,
      message: `数据迁移成功，从版本 ${migrationStatus.fromVersion} 升级到版本 ${DATA_VERSION.CURRENT}`,
      warnings
    };

  } catch (error) {
    return {
      success: false,
      version: migrationStatus.fromVersion,
      migratedStocks: [],
      migratedGroups: [],
      message: `数据迁移失败: ${error instanceof Error ? error.message : '未知错误'}`,
      warnings
    };
  }
}

/**
 * 创建迁移后的完整数据结构
 */
export function createMigratedUserData(
  originalData: any,
  migrationResult: MigrationResult
): UserStockData {
  return {
    userId: originalData.userId || '',
    stocks: migrationResult.migratedStocks,
    groups: migrationResult.migratedGroups,
    lastModified: new Date().toISOString(),
    deviceInfo: originalData.deviceInfo || {
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    },
    version: migrationResult.version
  };
}

/**
 * 备份原始数据
 */
export function createDataBackup(data: any): string {
  try {
    const backup = {
      ...data,
      backupTimestamp: new Date().toISOString(),
      backupVersion: data.version || 1
    };
    return JSON.stringify(backup);
  } catch (error) {
    throw new Error(`创建数据备份失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 从备份恢复数据
 */
export function restoreFromBackup(backupString: string): any {
  try {
    const backup = JSON.parse(backupString);
    // 移除备份相关字段
    const { backupTimestamp, backupVersion, ...originalData } = backup;
    return originalData;
  } catch (error) {
    throw new Error(`从备份恢复数据失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}
