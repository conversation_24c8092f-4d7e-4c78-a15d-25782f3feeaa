import {
  UserIdentity,
  SyncRequest,
  SyncResponse,
  UserDataBackup,
  StockInfo,
  StockGroup,
  ApiResponse
} from '@/types/stock';

// API 基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8787';

/**
 * API 请求错误类
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * 通用 API 请求函数
 */
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultHeaders = {
    'Content-Type': 'application/json',
    'User-Agent': navigator.userAgent,
  };

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new ApiError(
        data.message || `HTTP ${response.status}`,
        response.status,
        data.code
      );
    }

    return data;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    
    // 网络错误或其他错误
    throw new ApiError(
      error instanceof Error ? error.message : '网络请求失败',
      0,
      'NETWORK_ERROR'
    );
  }
}

/**
 * 用户管理 API
 */
export const userApi = {
  /**
   * 生成设备ID
   */
  async generateDeviceId(additionalInfo?: Record<string, any>): Promise<{ deviceId: string }> {
    const response = await apiRequest<{ deviceId: string }>('/api/user/device-id', {
      method: 'POST',
      body: JSON.stringify({ additionalInfo }),
    });
    
    if (!response.success) {
      throw new ApiError(response.message || 'Failed to generate device ID');
    }
    
    return response.data!;
  },

  /**
   * 同步用户股票数据
   */
  async syncStockData(syncRequest: SyncRequest): Promise<SyncResponse['data']> {
    const response = await apiRequest<SyncResponse['data']>('/api/user/sync', {
      method: 'POST',
      body: JSON.stringify(syncRequest),
    });
    
    if (!response.success) {
      throw new ApiError(response.message || 'Failed to sync stock data');
    }
    
    return response.data!;
  },

  /**
   * 获取用户股票数据
   */
  async getUserStocks(userId: string): Promise<{
    stocks: StockInfo[];
    groups?: StockGroup[]; // 可选的分组数据，保持向后兼容性
    lastModified: string;
    version: number;
    deviceInfo?: any;
  }> {
    const response = await apiRequest<{
      stocks: StockInfo[];
      groups?: StockGroup[]; // 可选的分组数据
      lastModified: string;
      version: number;
      deviceInfo?: any;
    }>(`/api/user/${userId}/stocks`);

    if (!response.success) {
      throw new ApiError(response.message || 'Failed to get user stocks');
    }

    return response.data!;
  },

  /**
   * 删除用户数据
   */
  async deleteUserData(userId: string): Promise<void> {
    const response = await apiRequest(`/api/user/${userId}`, {
      method: 'DELETE',
    });
    
    if (!response.success) {
      throw new ApiError(response.message || 'Failed to delete user data');
    }
  },

  /**
   * 创建数据备份
   */
  async createBackup(userId: string): Promise<UserDataBackup> {
    const response = await apiRequest<UserDataBackup>(`/api/user/${userId}/backup`);
    
    if (!response.success) {
      throw new ApiError(response.message || 'Failed to create backup');
    }
    
    return response.data!;
  },

  /**
   * 从备份恢复数据
   */
  async restoreFromBackup(backup: UserDataBackup): Promise<void> {
    const response = await apiRequest('/api/user/restore', {
      method: 'POST',
      body: JSON.stringify(backup),
    });
    
    if (!response.success) {
      throw new ApiError(response.message || 'Failed to restore from backup');
    }
  },

  /**
   * 获取用户数据统计
   */
  async getUserStats(userId: string): Promise<{
    stockCount: number;
    lastModified: string | null;
    version: number;
    deviceInfo: any;
  }> {
    const response = await apiRequest<{
      stockCount: number;
      lastModified: string | null;
      version: number;
      deviceInfo: any;
    }>(`/api/user/${userId}/stats`);
    
    if (!response.success) {
      throw new ApiError(response.message || 'Failed to get user stats');
    }
    
    return response.data!;
  },

  /**
   * 同步用户分组数据
   */
  async syncGroupData(userId: string, groups: StockGroup[]): Promise<{
    success: boolean;
    groups: StockGroup[];
    lastModified: string;
  }> {
    const response = await apiRequest<{
      success: boolean;
      groups: StockGroup[];
      lastModified: string;
    }>('/api/user/sync-groups', {
      method: 'POST',
      body: JSON.stringify({
        userId,
        groups,
        timestamp: new Date().toISOString()
      }),
    });

    if (!response.success) {
      throw new ApiError(response.message || 'Failed to sync group data');
    }

    return response.data!;
  },

  /**
   * 获取用户分组数据
   */
  async getUserGroups(userId: string): Promise<{
    groups: StockGroup[];
    lastModified: string;
    version: number;
  }> {
    const response = await apiRequest<{
      groups: StockGroup[];
      lastModified: string;
      version: number;
    }>(`/api/user/${userId}/groups`);

    if (!response.success) {
      throw new ApiError(response.message || 'Failed to get user groups');
    }

    return response.data!;
  },

  /**
   * 验证分组数据
   */
  validateGroupData(groups: StockGroup[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!Array.isArray(groups)) {
      errors.push('分组数据必须是数组');
      return { isValid: false, errors };
    }

    const groupIds = new Set<string>();
    const groupNames = new Set<string>();

    for (const group of groups) {
      // 检查必需字段
      if (!group.id || typeof group.id !== 'string') {
        errors.push('分组ID不能为空且必须是字符串');
      } else if (groupIds.has(group.id)) {
        errors.push(`重复的分组ID: ${group.id}`);
      } else {
        groupIds.add(group.id);
      }

      if (!group.name || typeof group.name !== 'string') {
        errors.push('分组名称不能为空且必须是字符串');
      } else if (groupNames.has(group.name)) {
        errors.push(`重复的分组名称: ${group.name}`);
      } else {
        groupNames.add(group.name);
      }

      if (typeof group.order !== 'number') {
        errors.push(`分组 ${group.name} 的排序值必须是数字`);
      }

      if (!group.createdAt || typeof group.createdAt !== 'string') {
        errors.push(`分组 ${group.name} 的创建时间不能为空且必须是字符串`);
      }
    }

    // 检查是否有默认分组
    const hasDefaultGroup = groups.some(group => group.isDefault);
    if (!hasDefaultGroup) {
      errors.push('必须至少有一个默认分组');
    }

    return { isValid: errors.length === 0, errors };
  },

  /**
   * 数据版本迁移处理
   */
  migrateUserData(userData: any): {
    stocks: StockInfo[];
    groups: StockGroup[];
    version: number;
  } {
    const currentVersion = 2; // 当前数据版本
    const dataVersion = userData.version || 1;

    let migratedStocks = userData.stocks || [];
    let migratedGroups = userData.groups || [];

    // 版本1到版本2的迁移：添加分组支持
    if (dataVersion < 2) {
      // 确保有默认分组
      if (migratedGroups.length === 0) {
        migratedGroups = [{
          id: 'default',
          name: '默认分组',
          order: 0,
          createdAt: new Date().toISOString(),
          isDefault: true,
          color: '#6B7280',
          description: '未分组的股票将自动归入此分组'
        }];
      }

      // 确保所有股票都有分组信息
      migratedStocks = migratedStocks.map((stock: any) => ({
        ...stock,
        groupId: stock.groupId || 'default'
      }));
    }

    return {
      stocks: migratedStocks,
      groups: migratedGroups,
      version: currentVersion
    };
  },
};

/**
 * 设备指纹生成工具
 */
export class DeviceFingerprint {
  /**
   * 生成浏览器指纹
   */
  static async generate(): Promise<string> {
    const components = [
      navigator.userAgent,
      navigator.language,
      navigator.platform,
      screen.width + 'x' + screen.height,
      screen.colorDepth,
      new Date().getTimezoneOffset(),
      navigator.hardwareConcurrency || 0,
      (navigator as any).deviceMemory || 0,
    ];

    // 添加 Canvas 指纹
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Device fingerprint', 2, 2);
        components.push(canvas.toDataURL());
      }
    } catch (e) {
      // Canvas 指纹生成失败，忽略
    }

    // 生成哈希
    const data = components.join('|');
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }

    return `fp_${Math.abs(hash).toString(36)}_${Date.now().toString(36)}`;
  }

  /**
   * 获取或生成设备ID
   */
  static async getOrCreateDeviceId(): Promise<string> {
    const STORAGE_KEY = 'device_id';
    
    // 尝试从 localStorage 获取
    let deviceId = localStorage.getItem(STORAGE_KEY);
    
    if (!deviceId) {
      // 生成新的设备ID
      deviceId = await this.generate();
      localStorage.setItem(STORAGE_KEY, deviceId);
    }
    
    return deviceId;
  }
}

/**
 * 用户身份管理工具
 */
export class UserIdentityManager {
  private static readonly USER_ID_KEY = 'user_id';
  private static readonly SESSION_ID_KEY = 'session_id';

  /**
   * 创建用户标识
   */
  static async createUserIdentity(customUserId?: string): Promise<UserIdentity> {
    const deviceId = await DeviceFingerprint.getOrCreateDeviceId();
    
    let userId = customUserId;
    if (!userId) {
      userId = localStorage.getItem(this.USER_ID_KEY) || deviceId;
      localStorage.setItem(this.USER_ID_KEY, userId);
    }

    const sessionId = this.generateSessionId();
    localStorage.setItem(this.SESSION_ID_KEY, sessionId);

    return {
      deviceId,
      userId,
      sessionId,
    };
  }

  /**
   * 获取当前用户标识
   */
  static async getCurrentUserIdentity(): Promise<UserIdentity> {
    const deviceId = await DeviceFingerprint.getOrCreateDeviceId();
    const userId = localStorage.getItem(this.USER_ID_KEY) || deviceId;
    const sessionId = localStorage.getItem(this.SESSION_ID_KEY) || this.generateSessionId();

    return {
      deviceId,
      userId,
      sessionId,
    };
  }

  /**
   * 设置自定义用户ID
   */
  static setCustomUserId(userId: string): void {
    localStorage.setItem(this.USER_ID_KEY, userId);
  }

  /**
   * 清除用户标识
   */
  static clearUserIdentity(): void {
    localStorage.removeItem(this.USER_ID_KEY);
    localStorage.removeItem(this.SESSION_ID_KEY);
  }

  /**
   * 生成会话ID
   */
  private static generateSessionId(): string {
    return `session_${Date.now().toString(36)}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
