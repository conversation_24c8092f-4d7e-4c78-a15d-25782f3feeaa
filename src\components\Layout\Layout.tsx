import React, { useState, useEffect } from 'react';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { MainContent } from './MainContent';
import { useStockList } from '@/hooks/useStockData';
import { X } from 'lucide-react';

interface LayoutProps {
  /** 子组件 */
  children?: React.ReactNode;
}

/**
 * 应用主布局组件
 */
export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [selectedStock, setSelectedStock] = useState<string | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isDark, setIsDark] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  const { stocks } = useStockList();

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 自动选择第一个股票
  useEffect(() => {
    if (stocks.length > 0 && !selectedStock) {
      setSelectedStock(stocks[0].code);
    }
  }, [stocks, selectedStock]);

  // 移动端自动关闭侧边栏
  useEffect(() => {
    if (isMobile) {
      setIsSidebarOpen(false);
    }
  }, [isMobile]);

  // 处理股票选择
  const handleStockSelect = (code: string) => {
    setSelectedStock(code);
    // 移动端选择后关闭侧边栏
    if (isMobile) {
      setIsSidebarOpen(false);
    }
  };

  // 处理主题切换
  const handleThemeToggle = () => {
    setIsDark(!isDark);
    // 这里可以添加主题持久化逻辑
    document.documentElement.classList.toggle('dark', !isDark);
  };

  // 处理关闭资金流向窗口
  const handleCloseFlowWindow = () => {
    setSelectedStock(null);
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${isDark ? 'dark' : ''}`}>
      {/* 页面头部 */}
      <Header
        showMenuButton={isMobile}
        onMenuClick={() => setIsSidebarOpen(true)}
        isDark={isDark}
        onThemeToggle={handleThemeToggle}
      />

      {/* 主要内容区域 - 全屏股票管理模块 */}
      <div className="relative h-[calc(100vh-64px)] w-full overflow-hidden">
        {/* 股票管理模块 - 占据整个页面 */}
        <div className="absolute inset-0 w-full h-full overflow-hidden">
          <Sidebar
            selectedStock={selectedStock}
            onStockSelect={handleStockSelect}
            isOpen={isMobile ? isSidebarOpen : true}
            onClose={isMobile ? () => setIsSidebarOpen(false) : undefined}
            className="w-full h-full relative transform-none"
          />
        </div>

        {/* 股票资金流向模块 - 右下角小窗口 */}
        {selectedStock && (
          <div className="absolute bottom-4 right-4 w-[500px] h-[400px] bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden z-10">
            <div className="h-full flex flex-col">
              {/* 小窗口标题栏 */}
              <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
                <div className="flex items-center gap-2">
                  <h3 className="text-sm font-medium text-gray-900">资金流向</h3>
                  <div className="text-xs text-gray-500">
                    {stocks.find(s => s.code === selectedStock)?.name || selectedStock}
                  </div>
                </div>
                <button
                  onClick={handleCloseFlowWindow}
                  className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-200 rounded transition-colors"
                  title="关闭资金流向窗口"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              {/* 小窗口内容 */}
              <div className="flex-1 overflow-hidden">
                {children || (
                  <MainContent
                    selectedStock={selectedStock}
                    className="h-full"
                  />
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
