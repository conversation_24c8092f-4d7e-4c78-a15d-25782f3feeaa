import { useState, useCallback, useEffect } from 'react';
import { Stock } from '@/types';
import { validateStockCode, formatStockCode, isDuplicateCode } from '@/utils/validation';
import { useCloudStorage } from './useCloudStorage';
import { useStockGroups } from './useStockGroups';
import { StockInfo, MoveStockToGroupRequest } from '@/types/stock';
import {
  migrateData,
  detectMigrationStatus,
  createDataBackup
} from '@/utils/dataMigration';

// 常见股票名称映射
const STOCK_NAME_MAP: Record<string, string> = {
  '000001': '平安银行',
  '000002': '万科A',
  '000858': '五粮液',
  '600036': '招商银行',
  '600121': '郑州煤电',
  '600519': '贵州茅台',
  '002415': '海康威视',
  '600276': '恒瑞医药',
  '000725': '京东方A',
  '600793': '宜宾纸业',
  '603067': '振华股份',
  // 可以继续添加更多股票
};

/**
 * 获取股票名称
 * @param code 股票代码
 * @returns 股票名称
 */
function getStockName(code: string): string {
  return STOCK_NAME_MAP[code] || `股票${code}`;
}

/**
 * 数据迁移：将旧的股票数据迁移到新的分组结构
 * @param stocks 股票列表
 * @param defaultGroup 默认分组
 * @returns 迁移后的股票列表
 */
function migrateStocksToGroups(stocks: Stock[], defaultGroup: any): Stock[] {
  if (!defaultGroup) {
    // 如果没有默认分组，暂时不进行迁移
    return stocks;
  }

  return stocks.map(stock => {
    // 如果股票没有分组信息，将其分配到默认分组
    if (!stock.groupId) {
      return {
        ...stock,
        groupId: defaultGroup.id
      };
    }
    return stock;
  });
}

interface UseStockListReturn {
  stocks: Stock[];
  addStock: (code: string, name?: string, groupId?: string) => Promise<{ success: boolean; message?: string }>;
  removeStock: (code: string) => void;
  clearAllStocks: () => void;
  moveStockToGroup: (request: MoveStockToGroupRequest) => Promise<{ success: boolean; message?: string }>;
  getStocksByGroup: (groupId: string) => Stock[];
  isLoading: boolean;
  error: string | null;
  // 云端存储相关
  syncStatus: any;
  userIdentity: any;
  lastModified: string;
  forceSyncToCloud: () => Promise<boolean>;
  forceLoadFromCloud: () => Promise<boolean>;
  createBackup: () => Promise<any>;
  restoreFromBackup: (backup: any) => Promise<boolean>;
  deleteCloudData: () => Promise<boolean>;
  setCustomUserId: (userId: string) => Promise<boolean>;
  getUserStats: () => Promise<any>;
  // 数据迁移相关
  checkMigrationStatus: () => { isRequired: boolean; fromVersion: number; toVersion: number };
  performMigration: () => Promise<{ success: boolean; message: string; warnings: string[] }>;
  createDataBackup: () => Promise<string>;
  restoreFromDataBackup: (backupString: string) => Promise<boolean>;
}

const STORAGE_KEY = 'gupiao-stock-list';

/**
 * 股票列表管理Hook
 */
export function useStockList(): UseStockListReturn {
  const [stocks, setStocks] = useState<Stock[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastModified, setLastModified] = useState<string>(new Date().toISOString());
  const [migrationStatus, setMigrationStatus] = useState<{ isRequired: boolean; fromVersion: number; toVersion: number } | null>(null);

  // 云端存储管理
  const cloudStorage = useCloudStorage();

  // 分组管理
  const { defaultGroup } = useStockGroups();

  // 初始化数据加载
  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);
      try {
        // 首先从 localStorage 加载数据
        const savedStocks = localStorage.getItem(STORAGE_KEY);
        const savedLastModified = localStorage.getItem(`${STORAGE_KEY}_lastModified`);
        const savedVersion = localStorage.getItem(`${STORAGE_KEY}_version`);

        let localData: any = {
          stocks: [],
          version: 1,
          lastModified: new Date().toISOString()
        };

        if (savedStocks) {
          try {
            localData = {
              stocks: JSON.parse(savedStocks),
              version: savedVersion ? parseInt(savedVersion) : 1,
              lastModified: savedLastModified || new Date().toISOString()
            };
          } catch (error) {
            console.error('解析本地数据失败:', error);
            setError('本地数据格式错误，将重新初始化');
          }
        }

        // 检查是否需要数据迁移
        const migrationStatusCheck = detectMigrationStatus(localData);
        setMigrationStatus(migrationStatusCheck);

        let finalStocks: Stock[] = [];
        let finalLastModified = localData.lastModified;

        if (migrationStatusCheck.isRequired) {
          // 执行数据迁移
          console.log('检测到需要数据迁移，开始迁移...');

          try {
            // 创建备份
            const backup = createDataBackup(localData);
            localStorage.setItem(`${STORAGE_KEY}_backup`, backup);

            // 执行迁移
            const migrationResult = migrateData(localData);

            if (migrationResult.success) {
              finalStocks = migrationResult.migratedStocks.map(stockInfo => ({
                code: stockInfo.code,
                name: stockInfo.name,
                groupId: stockInfo.groupId
              }));

              finalLastModified = new Date().toISOString();

              // 保存迁移后的数据
              saveToStorage(finalStocks, finalLastModified);
              localStorage.setItem(`${STORAGE_KEY}_version`, migrationResult.version.toString());

              console.log('数据迁移成功:', migrationResult.message);
              if (migrationResult.warnings.length > 0) {
                console.warn('迁移警告:', migrationResult.warnings);
              }
            } else {
              console.error('数据迁移失败:', migrationResult.message);
              setError('数据迁移失败: ' + migrationResult.message);
              // 使用原始数据
              finalStocks = localData.stocks || [];
            }
          } catch (migrationError) {
            console.error('迁移过程出错:', migrationError);
            setError('迁移过程出错，使用原始数据');
            finalStocks = localData.stocks || [];
          }
        } else {
          // 不需要迁移，直接使用数据
          finalStocks = localData.stocks || [];
        }

        // 设置本地数据
        setStocks(finalStocks);
        setLastModified(finalLastModified);

        // 如果在线，尝试从云端同步数据
        if (cloudStorage.syncStatus.isOnline && cloudStorage.userIdentity) {
          try {
            const cloudData = await cloudStorage.getFromCloud();

            if (cloudData && cloudData.stocks.length > 0) {
              // 比较时间戳，决定使用哪个数据
              const cloudTime = new Date(cloudData.lastModified).getTime();
              const localTime = new Date(finalLastModified).getTime();

              if (cloudTime > localTime) {
                // 云端数据更新，使用云端数据
                const convertedStocks = cloudData.stocks.map(stock => ({
                  code: stock.code,
                  name: stock.name,
                  groupId: stock.groupId, // 保留分组信息
                }));

                // 对云端数据也进行迁移处理
                const migratedCloudStocks = migrateStocksToGroups(convertedStocks, defaultGroup);

                setStocks(migratedCloudStocks);
                setLastModified(cloudData.lastModified);
                saveToStorage(migratedCloudStocks, cloudData.lastModified);
              } else if (localTime > cloudTime && finalStocks.length > 0) {
                // 本地数据更新，同步到云端
                const stockInfos: StockInfo[] = finalStocks.map(stock => ({
                  code: stock.code,
                  name: stock.name,
                  addedAt: new Date().toISOString(),
                  groupId: stock.groupId, // 包含分组信息
                }));

                await cloudStorage.syncToCloud(stockInfos, finalLastModified);
              }
            } else if (finalStocks.length > 0) {
              // 云端无数据，上传本地数据
              const stockInfos: StockInfo[] = finalStocks.map(stock => ({
                code: stock.code,
                name: stock.name,
                addedAt: new Date().toISOString(),
                groupId: stock.groupId, // 包含分组信息
              }));

              await cloudStorage.syncToCloud(stockInfos, finalLastModified);
            }
          } catch (cloudError) {
            console.warn('云端数据同步失败，使用本地数据:', cloudError);
          }
        }
      } catch (err) {
        console.error('初始化数据失败:', err);
        setError('初始化数据失败');
      } finally {
        setIsLoading(false);
      }
    };

    if (cloudStorage.userIdentity) {
      initializeData();
    }
  }, [cloudStorage.userIdentity, cloudStorage.syncStatus.isOnline]);

  // 保存股票列表到localStorage和云端
  const saveToStorage = useCallback((stockList: Stock[], timestamp?: string) => {
    const saveTime = timestamp || new Date().toISOString();

    try {
      // 保存到 localStorage
      localStorage.setItem(STORAGE_KEY, JSON.stringify(stockList));
      localStorage.setItem(`${STORAGE_KEY}_lastModified`, saveTime);
      localStorage.setItem(`${STORAGE_KEY}_version`, '2'); // 当前版本

      setLastModified(saveTime);

      // 标记本地数据有变更，触发云端同步
      if (cloudStorage.syncStatus.isOnline) {
        cloudStorage.markLocalChanges();

        // 异步同步到云端
        const stockInfos: StockInfo[] = stockList.map(stock => ({
          code: stock.code,
          name: stock.name,
          addedAt: new Date().toISOString(),
          groupId: stock.groupId, // 包含分组信息
        }));

        cloudStorage.syncToCloud(stockInfos, saveTime).catch(error => {
          console.warn('云端同步失败:', error);
        });
      }
    } catch (err) {
      console.error('保存股票列表失败:', err);
      setError('保存股票列表失败');
    }
  }, [cloudStorage]);

  // 添加股票
  const addStock = useCallback(async (code: string, name?: string, groupId?: string): Promise<{ success: boolean; message?: string }> => {
    setIsLoading(true);
    setError(null);

    try {
      // 验证股票代码
      const validation = validateStockCode(code);
      if (!validation.isValid) {
        return { success: false, message: validation.message };
      }

      const formattedCode = formatStockCode(code);
      const existingCodes = stocks.map(stock => stock.code);

      // 检查重复
      if (isDuplicateCode(formattedCode, existingCodes)) {
        return { success: false, message: '股票代码已存在' };
      }

      // 使用传入的名称，如果没有则使用映射表或默认格式
      const stockName = name || getStockName(formattedCode);

      // 确定分组ID，如果没有指定则使用默认分组
      const targetGroupId = groupId || (defaultGroup ? defaultGroup.id : undefined);

      // 创建新股票对象
      const newStock: Stock = {
        code: formattedCode,
        name: stockName,
        groupId: targetGroupId,
      };

      // 更新股票列表
      const updatedStocks = [...stocks, newStock];
      setStocks(updatedStocks);
      saveToStorage(updatedStocks);

      return { success: true, message: '股票添加成功' };
    } catch (err) {
      const errorMessage = '添加股票失败';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [stocks, saveToStorage]);

  // 删除股票
  const removeStock = useCallback((code: string) => {
    setError(null);
    
    try {
      const updatedStocks = stocks.filter(stock => stock.code !== code);
      setStocks(updatedStocks);
      saveToStorage(updatedStocks);
    } catch (err) {
      console.error('删除股票失败:', err);
      setError('删除股票失败');
    }
  }, [stocks, saveToStorage]);

  // 清空所有股票
  const clearAllStocks = useCallback(() => {
    setError(null);
    
    try {
      setStocks([]);
      saveToStorage([]);
    } catch (err) {
      console.error('清空股票列表失败:', err);
      setError('清空股票列表失败');
    }
  }, [saveToStorage]);

  // 移动股票到指定分组
  const moveStockToGroup = useCallback(async (request: MoveStockToGroupRequest): Promise<{ success: boolean; message?: string }> => {
    try {
      const stockIndex = stocks.findIndex(stock => stock.code === request.stockCode);
      if (stockIndex === -1) {
        return { success: false, message: '股票不存在' };
      }

      const updatedStocks = [...stocks];
      updatedStocks[stockIndex] = {
        ...updatedStocks[stockIndex],
        groupId: request.targetGroupId || (defaultGroup ? defaultGroup.id : undefined)
      };

      setStocks(updatedStocks);
      saveToStorage(updatedStocks);

      return { success: true, message: '股票分组移动成功' };
    } catch (err) {
      console.error('移动股票到分组失败:', err);
      const errorMessage = '移动股票到分组失败';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    }
  }, [stocks, defaultGroup, saveToStorage]);

  // 根据分组ID获取股票列表
  const getStocksByGroup = useCallback((groupId: string): Stock[] => {
    return stocks.filter(stock => {
      // 如果股票没有分组信息，且查询的是默认分组，则包含该股票
      if (!stock.groupId && defaultGroup && groupId === defaultGroup.id) {
        return true;
      }
      return stock.groupId === groupId;
    });
  }, [stocks, defaultGroup]);

  // 强制同步到云端
  const forceSyncToCloud = useCallback(async (): Promise<boolean> => {
    if (!cloudStorage.syncStatus.isOnline || !cloudStorage.userIdentity) {
      return false;
    }

    setIsLoading(true);
    try {
      const stockInfos: StockInfo[] = stocks.map(stock => ({
        code: stock.code,
        name: stock.name,
        addedAt: new Date().toISOString(),
        groupId: stock.groupId, // 包含分组信息
      }));

      const result = await cloudStorage.syncToCloud(stockInfos, lastModified, true);
      return result !== null;
    } catch (error) {
      console.error('强制同步失败:', error);
      setError('强制同步失败');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [stocks, lastModified, cloudStorage]);

  // 从云端强制拉取数据
  const forceLoadFromCloud = useCallback(async (): Promise<boolean> => {
    if (!cloudStorage.syncStatus.isOnline || !cloudStorage.userIdentity) {
      return false;
    }

    setIsLoading(true);
    try {
      const cloudData = await cloudStorage.getFromCloud();

      if (cloudData) {
        const convertedStocks = cloudData.stocks.map(stock => ({
          code: stock.code,
          name: stock.name,
          groupId: stock.groupId, // 保留分组信息
        }));

        // 对云端数据进行迁移处理
        const migratedStocks = migrateStocksToGroups(convertedStocks, defaultGroup);

        setStocks(migratedStocks);
        setLastModified(cloudData.lastModified);
        saveToStorage(migratedStocks, cloudData.lastModified);
        return true;
      }

      return false;
    } catch (error) {
      console.error('从云端加载失败:', error);
      setError('从云端加载失败');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [cloudStorage, saveToStorage]);

  return {
    stocks,
    addStock,
    removeStock,
    clearAllStocks,
    moveStockToGroup,
    getStocksByGroup,
    isLoading,
    error,
    // 云端存储相关
    syncStatus: cloudStorage.syncStatus,
    userIdentity: cloudStorage.userIdentity,
    lastModified,
    forceSyncToCloud,
    forceLoadFromCloud,
    createBackup: cloudStorage.createBackup,
    restoreFromBackup: cloudStorage.restoreFromBackup,
    deleteCloudData: cloudStorage.deleteCloudData,
    setCustomUserId: cloudStorage.setCustomUserId,
    getUserStats: cloudStorage.getUserStats,
    // 数据迁移相关
    checkMigrationStatus: () => migrationStatus || { isRequired: false, fromVersion: 2, toVersion: 2 },
    performMigration: async () => {
      try {
        const currentData = {
          stocks: stocks.map(stock => ({
            code: stock.code,
            name: stock.name,
            addedAt: new Date().toISOString(),
            groupId: stock.groupId
          })),
          version: 1 // 假设当前是旧版本
        };

        const migrationResult = migrateData(currentData);

        if (migrationResult.success) {
          const migratedStocks = migrationResult.migratedStocks.map(stockInfo => ({
            code: stockInfo.code,
            name: stockInfo.name,
            groupId: stockInfo.groupId
          }));

          setStocks(migratedStocks);
          saveToStorage(migratedStocks);
          setMigrationStatus({ isRequired: false, fromVersion: 2, toVersion: 2 });
        }

        return {
          success: migrationResult.success,
          message: migrationResult.message,
          warnings: migrationResult.warnings
        };
      } catch (error) {
        return {
          success: false,
          message: `迁移失败: ${error instanceof Error ? error.message : '未知错误'}`,
          warnings: []
        };
      }
    },
    createDataBackup: async () => {
      try {
        const currentData = {
          stocks: stocks.map(stock => ({
            code: stock.code,
            name: stock.name,
            addedAt: new Date().toISOString(),
            groupId: stock.groupId
          })),
          version: 2,
          lastModified
        };
        return createDataBackup(currentData);
      } catch (error) {
        throw new Error(`创建备份失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    },
    restoreFromDataBackup: async (backupString: string) => {
      try {
        const { restoreFromBackup } = await import('@/utils/dataMigration');
        const restoredData = restoreFromBackup(backupString);

        if (restoredData.stocks) {
          const restoredStocks = restoredData.stocks.map((stock: any) => ({
            code: stock.code,
            name: stock.name,
            groupId: stock.groupId
          }));

          setStocks(restoredStocks);
          saveToStorage(restoredStocks);
          return true;
        }
        return false;
      } catch (error) {
        console.error('从备份恢复失败:', error);
        return false;
      }
    },
  };
}
