import { useState, useCallback } from 'react';
import { 
  AlertCircle, 
  CheckCircle, 
  RefreshCw, 
  Download, 
  Upload, 
  Info,
  Clock,
  Database
} from 'lucide-react';

interface MigrationStatusProps {
  migrationStatus: {
    isRequired: boolean;
    fromVersion: number;
    toVersion: number;
  };
  onPerformMigration: () => Promise<{ success: boolean; message: string; warnings: string[] }>;
  onCreateBackup: () => Promise<string>;
  onRestoreBackup: (backup: string) => Promise<boolean>;
  isLoading?: boolean;
}

export function MigrationStatus({
  migrationStatus,
  onPerformMigration,
  onCreateBackup,
  onRestoreBackup,
  isLoading = false
}: MigrationStatusProps) {
  const [isMigrating, setIsMigrating] = useState(false);
  const [migrationResult, setMigrationResult] = useState<{
    success: boolean;
    message: string;
    warnings: string[];
  } | null>(null);
  const [, setBackupFile] = useState<string | null>(null);

  // 执行迁移
  const handleMigration = useCallback(async () => {
    setIsMigrating(true);
    setMigrationResult(null);

    try {
      const result = await onPerformMigration();
      setMigrationResult(result);
    } catch (error) {
      setMigrationResult({
        success: false,
        message: `迁移失败: ${error instanceof Error ? error.message : '未知错误'}`,
        warnings: []
      });
    } finally {
      setIsMigrating(false);
    }
  }, [onPerformMigration]);

  // 创建备份
  const handleCreateBackup = useCallback(async () => {
    try {
      const backup = await onCreateBackup();
      setBackupFile(backup);
      
      // 下载备份文件
      const blob = new Blob([backup], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `stock-data-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('创建备份失败:', error);
    }
  }, [onCreateBackup]);

  // 恢复备份
  const handleRestoreBackup = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      const success = await onRestoreBackup(text);
      
      if (success) {
        setMigrationResult({
          success: true,
          message: '数据恢复成功',
          warnings: []
        });
      } else {
        setMigrationResult({
          success: false,
          message: '数据恢复失败',
          warnings: []
        });
      }
    } catch (error) {
      setMigrationResult({
        success: false,
        message: `恢复失败: ${error instanceof Error ? error.message : '未知错误'}`,
        warnings: []
      });
    }

    // 清空文件输入
    event.target.value = '';
  }, [onRestoreBackup]);

  // 如果不需要迁移，显示当前状态
  if (!migrationStatus.isRequired) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center gap-2">
          <CheckCircle className="w-5 h-5 text-green-600" />
          <div>
            <h3 className="font-medium text-green-900">数据已是最新版本</h3>
            <p className="text-sm text-green-700 mt-1">
              当前版本: v{migrationStatus.toVersion}，无需迁移
            </p>
          </div>
        </div>
        
        {/* 备份选项 */}
        <div className="mt-4 pt-4 border-t border-green-200">
          <div className="flex items-center gap-2 mb-2">
            <Database className="w-4 h-4 text-green-600" />
            <span className="text-sm font-medium text-green-900">数据管理</span>
          </div>
          <div className="flex gap-2">
            <button
              onClick={handleCreateBackup}
              className="btn btn-sm btn-secondary flex items-center gap-1"
            >
              <Download className="w-3 h-3" />
              创建备份
            </button>
            <label className="btn btn-sm btn-secondary flex items-center gap-1 cursor-pointer">
              <Upload className="w-3 h-3" />
              恢复备份
              <input
                type="file"
                accept=".json"
                onChange={handleRestoreBackup}
                className="hidden"
              />
            </label>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div className="flex items-start gap-3">
        <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
        <div className="flex-1">
          <h3 className="font-medium text-yellow-900">需要数据迁移</h3>
          <p className="text-sm text-yellow-700 mt-1">
            检测到旧版本数据（v{migrationStatus.fromVersion}），需要升级到 v{migrationStatus.toVersion} 以支持分组功能。
          </p>

          {/* 迁移信息 */}
          <div className="mt-3 p-3 bg-yellow-100 rounded border border-yellow-300">
            <div className="flex items-center gap-2 mb-2">
              <Info className="w-4 h-4 text-yellow-700" />
              <span className="text-sm font-medium text-yellow-800">迁移说明</span>
            </div>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• 现有股票数据将自动归入默认分组</li>
              <li>• 原有数据将被自动备份</li>
              <li>• 迁移过程不会丢失任何股票信息</li>
              <li>• 迁移完成后可享受分组管理功能</li>
            </ul>
          </div>

          {/* 操作按钮 */}
          <div className="mt-4 flex gap-2">
            <button
              onClick={handleMigration}
              disabled={isMigrating || isLoading}
              className="btn btn-primary flex items-center gap-2"
            >
              {isMigrating ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  迁移中...
                </>
              ) : (
                <>
                  <Database className="w-4 h-4" />
                  开始迁移
                </>
              )}
            </button>
            
            <button
              onClick={handleCreateBackup}
              disabled={isMigrating || isLoading}
              className="btn btn-secondary flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              先创建备份
            </button>
          </div>

          {/* 迁移结果 */}
          {migrationResult && (
            <div className={`mt-4 p-3 rounded border ${
              migrationResult.success 
                ? 'bg-green-100 border-green-300 text-green-800'
                : 'bg-red-100 border-red-300 text-red-800'
            }`}>
              <div className="flex items-center gap-2">
                {migrationResult.success ? (
                  <CheckCircle className="w-4 h-4" />
                ) : (
                  <AlertCircle className="w-4 h-4" />
                )}
                <span className="font-medium">{migrationResult.message}</span>
              </div>
              
              {migrationResult.warnings.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm font-medium">注意事项：</p>
                  <ul className="text-sm mt-1 space-y-1">
                    {migrationResult.warnings.map((warning, index) => (
                      <li key={index}>• {warning}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* 预估时间 */}
          <div className="mt-3 flex items-center gap-2 text-xs text-yellow-600">
            <Clock className="w-3 h-3" />
            <span>预估迁移时间: 少于1分钟</span>
          </div>
        </div>
      </div>
    </div>
  );
}
