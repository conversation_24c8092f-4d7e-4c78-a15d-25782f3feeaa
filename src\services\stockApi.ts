import { 
  ApiResponse, 
  StockInfo, 
  ProcessedStockData, 
  BatchStockDataResponse, 
  StockLastUpdate, 
  ApiServiceStatus,
  StockApiError 
} from '@/types/stock';

/**
 * API基础配置
 */
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8787';

/**
 * 创建API错误
 */
const createApiError = (message: string, status?: number, details?: any): StockApiError => {
  const error = new Error(message) as StockApiError;
  error.status = status;
  error.details = details;
  return error;
};

/**
 * 通用API请求函数
 */
const apiRequest = async <T>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<ApiResponse<T>> => {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  };

  try {
    const response = await fetch(url, { ...defaultOptions, ...options });
    
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      throw createApiError(
        `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        errorText
      );
    }

    const data = await response.json();
    
    // 验证响应格式
    if (typeof data !== 'object' || !('success' in data)) {
      throw createApiError('Invalid API response format');
    }

    return data as ApiResponse<T>;
  } catch (error) {
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw createApiError('Network error: Unable to connect to server');
    }
    throw error;
  }
};

/**
 * 股票管理API
 */
export const stockManagementApi = {
  /**
   * 获取股票列表
   */
  async getStocks(): Promise<StockInfo[]> {
    const response = await apiRequest<StockInfo[]>('/api/stocks');
    if (!response.success) {
      throw createApiError(response.message || 'Failed to get stocks');
    }
    return response.data || [];
  },

  /**
   * 添加股票
   */
  async addStock(code: string, name?: string): Promise<StockInfo> {
    const response = await apiRequest<StockInfo>('/api/stocks', {
      method: 'POST',
      body: JSON.stringify({ code, name }),
    });
    
    if (!response.success) {
      throw createApiError(response.message || 'Failed to add stock');
    }
    
    return response.data!;
  },

  /**
   * 删除股票
   */
  async removeStock(code: string): Promise<StockInfo> {
    const response = await apiRequest<StockInfo>(`/api/stocks/${code}`, {
      method: 'DELETE',
    });
    
    if (!response.success) {
      throw createApiError(response.message || 'Failed to remove stock');
    }
    
    return response.data!;
  },

  /**
   * 批量添加股票
   */
  async addStocksBatch(stocks: Array<{ code: string; name?: string }>): Promise<{
    added: StockInfo[];
    errors: string[];
    total: number;
  }> {
    const response = await apiRequest('/api/stocks/batch', {
      method: 'POST',
      body: JSON.stringify({ stocks }),
    });
    
    if (!response.success) {
      throw createApiError(response.message || 'Failed to add stocks');
    }
    
    return response.data as any;
  },

  /**
   * 清空所有股票
   */
  async clearAllStocks(): Promise<{ deletedCount: number }> {
    const response = await apiRequest('/api/stocks', {
      method: 'DELETE',
    });
    
    if (!response.success) {
      throw createApiError(response.message || 'Failed to clear stocks');
    }
    
    return response.data as any;
  },
};

/**
 * 股票数据API
 */
export const stockDataApi = {
  /**
   * 获取单个股票数据
   */
  async getStockData(code: string, limit: number = 240, useCache: boolean = true): Promise<ProcessedStockData> {
    const params = new URLSearchParams({
      limit: limit.toString(),
      cache: useCache.toString(),
    });
    
    const response = await apiRequest<ProcessedStockData>(`/api/data/${code}?${params}`);
    
    if (!response.success) {
      throw createApiError(response.message || 'Failed to get stock data');
    }
    
    return response.data!;
  },

  /**
   * 批量获取股票数据
   */
  async getBatchStockData(codes: string[], limit: number = 240, useCache: boolean = true): Promise<BatchStockDataResponse> {
    const params = new URLSearchParams({
      codes: codes.join(','),
      limit: limit.toString(),
      cache: useCache.toString(),
    });

    // 使用修复后的批量API端点
    const response = await apiRequest<BatchStockDataResponse>(`/api/stocks/batch-data?${params}`);

    if (!response.success) {
      throw createApiError(response.message || 'Failed to get batch stock data');
    }

    return response.data!;
  },

  /**
   * 获取股票最后更新时间
   */
  async getLastUpdate(code: string): Promise<StockLastUpdate> {
    const response = await apiRequest<StockLastUpdate>(`/api/data/${code}/last-update`);
    
    if (!response.success) {
      throw createApiError(response.message || 'Failed to get last update');
    }
    
    return response.data!;
  },

  /**
   * 清除股票缓存
   */
  async clearCache(code: string): Promise<{ code: string }> {
    const response = await apiRequest(`/api/data/${code}/cache`, {
      method: 'DELETE',
    });
    
    if (!response.success) {
      throw createApiError(response.message || 'Failed to clear cache');
    }
    
    return response.data as any;
  },

  /**
   * 获取API服务状态
   */
  async getServiceStatus(): Promise<ApiServiceStatus> {
    // 使用新的端点避免旧端点的缓存问题
    const response = await apiRequest<ApiServiceStatus>('/api/service-status');

    if (!response.success) {
      throw createApiError(response.message || 'Failed to get service status');
    }

    return response.data!;
  },
};

/**
 * 工具API
 */
export const utilsApi = {
  /**
   * 健康检查
   */
  async healthCheck(): Promise<any> {
    const response = await apiRequest('/health');
    return response;
  },

  /**
   * API测试
   */
  async testApi(): Promise<any> {
    const response = await apiRequest('/api/test');
    return response;
  },
};

/**
 * 导出便捷函数
 */
export const getStocks = stockManagementApi.getStocks;
export const addStock = stockManagementApi.addStock;
export const removeStock = stockManagementApi.removeStock;
export const getStockData = stockDataApi.getStockData;
export const getBatchStockData = stockDataApi.getBatchStockData;
export const getServiceStatus = stockDataApi.getServiceStatus;
